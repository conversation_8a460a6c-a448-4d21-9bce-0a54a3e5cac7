import React, { useEffect, useState, useCallback } from "react";
import { Input } from "@shadcn/ui/input";
import { Label } from "@shadcn/ui/label";
import { Button } from "@shadcn/ui/button";
import { useAuth } from "../contexts/AuthContext";
import api from "../utils/api";
import InventoryList from "../components/inventory/InventoryList";
import InventoryForm from "../components/inventory/InventoryForm";

const InventoryPage = () => {
  const { logout } = useAuth();

  const [inventories, setInventories] = useState([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [showForm, setShowForm] = useState(false);
  const [editingInventory, setEditingInventory] = useState(null);

  const [printData, setPrintData] = useState(null);

  // Fetch inventories from backend
  const fetchInventories = useCallback(async () => {
    setLoading(true);
    setError("");
    try {
      const response = await api.get(
        `/inventories${search ? `?search=${encodeURIComponent(search)}` : ""}`
      );
      setInventories(response.data);
    } catch (err) {
      console.error("Error fetching inventories:", err);
      setError(
        err.response?.data?.message || "Failed to load inventories. Please try again."
      );
    } finally {
      setLoading(false);
    }
  }, [search]);

  // Run on initial mount and whenever `search` changes
  useEffect(() => {
    fetchInventories();
  }, [fetchInventories]);

  // Handler: open form to create
  const handleCreate = () => {
    setEditingInventory(null);
    setShowForm(true);
  };

  // Handler: open form to edit
  const handleEdit = (inv) => {
    setEditingInventory(inv);
    setShowForm(true);
  };

  // Handler: delete inventory
  const handleDelete = async (id) => {
    const confirmed = window.confirm("Are you sure you want to delete this inventory?");
    if (!confirmed) return;

    try {
      await api.delete(`/inventories/${id}`);
      fetchInventories();
    } catch (err) {
      console.error("Error deleting inventory:", err);
      alert(err.response?.data?.message || "Failed to delete. Try again.");
    }
  };

  // Handler: form submit for create or update
  const handleFormSubmit = async (values) => {
    try {
      if (editingInventory) {
        await api.put(`/inventories/${editingInventory._id}`, values);
      } else {
        await api.post("/inventories", values);
      }
      setShowForm(false);
      setEditingInventory(null);
      fetchInventories();
    } catch (err) {
      console.error("Error saving inventory:", err);
      alert(err.response?.data?.message || "Failed to save. Try again.");
    }
  };

  // Handler: prepare print data and open print dialog
  const handlePrint = (filteredList) => {
    const columns = [
      { key: "name", header: "Name" },
      { key: "description", header: "Description" },
      { key: "createdAt", header: "Created At" },
    ];
    const rows = filteredList.map((inv) => ({
      name: inv.name,
      description: inv.description || "",
      createdAt: new Date(inv.createdAt).toLocaleDateString(),
    }));
    setPrintData({ columns, rows, title: "Inventory List" });

    // Wait a tick for print data to render, then call window.print()
    setTimeout(() => {
      window.print();
    }, 100);
  };

  return (
    <div className="px-4 py-6 max-w-5xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <h1 className="text-3xl font-bold">My Inventories</h1>
        <Button variant="outline" onClick={logout}>
          Logout
        </Button>
      </div>

      {/* Search & New Inventory */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 space-y-4 sm:space-y-0">
        <div className="w-full sm:w-1/2">
          <Label htmlFor="search" className="block font-medium mb-1">
            Search Inventories
          </Label>
          <Input
            id="search"
            placeholder="Search by name..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <Button onClick={handleCreate}>+ New Inventory</Button>
      </div>

      {/* Loading, Error, or Table */}
      {loading ? (
        <p className="text-center py-8">Loading inventories…</p>
      ) : error ? (
        <p className="text-red-500 text-center py-4">{error}</p>
      ) : (
        <InventoryList
          inventories={inventories}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onPrint={handlePrint}
        />
      )}

      {/* InventoryForm Modal */}
      {showForm && (
        <InventoryForm
          initialData={editingInventory}
          onSubmit={handleFormSubmit}
          onClose={() => {
            setShowForm(false);
            setEditingInventory(null);
          }}
        />
      )}

      {/* Printable Table (only visible in print) */}
      {printData && (
        <div className="print:block hidden mt-8">
          <h2 className="text-xl font-semibold mb-4">{printData.title}</h2>
          <table className="w-full border-collapse">
            <thead className="border-b">
              <tr>
                {printData.columns.map((col) => (
                  <th
                    key={col.key}
                    className="text-left px-2 py-1 border"
                  >
                    {col.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {printData.rows.map((row, idx) => (
                <tr
                  key={idx}
                  className={idx % 2 === 0 ? "bg-gray-100" : ""}
                >
                  {printData.columns.map((col) => (
                    <td key={col.key} className="px-2 py-1 border">
                      {row[col.key]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default InventoryPage;
