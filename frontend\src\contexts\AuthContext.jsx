import React, { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../utils/api";

// Create the context
const AuthContext = createContext();

// Custom hook for consuming
export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }) => {
  const navigate = useNavigate();

  // Initialize state from localStorage (if present)
  const [user, setUser] = useState(() => {
    try {
      const stored = localStorage.getItem("auth_user");
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  });

  const [token, setToken] = useState(() => {
    return localStorage.getItem("auth_token") || null;
  });

  // Whenever token changes, update Axios default header & localStorage
  useEffect(() => {
    if (token) {
      api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      localStorage.setItem("auth_token", token);
    } else {
      delete api.defaults.headers.common["Authorization"];
      localStorage.removeItem("auth_token");
    }
  }, [token]);

  // Whenever user changes, persist to localStorage
  useEffect(() => {
    if (user) {
      localStorage.setItem("auth_user", JSON.stringify(user));
    } else {
      localStorage.removeItem("auth_user");
    }
  }, [user]);

  // login function
  const login = async (email, password) => {
    // adjust the endpoint path if needed
    const response = await api.post("/auth/login", { email, password });
    // assume response.data = { user: { name, email, ... }, token: "<jwt>" }
    const { user: loggedInUser, token: jwtToken } = response.data;
    setUser(loggedInUser);
    setToken(jwtToken);
    return response.data;
  };

  // register function
  const register = async (name, email, password) => {
    const response = await api.post("/auth/register", { name, email, password });
    // assume same shape: { user: { … }, token: "<jwt>" }
    const { user: newUser, token: jwtToken } = response.data;
    setUser(newUser);
    setToken(jwtToken);
    return response.data;
  };

  // logout function
  const logout = () => {
    setUser(null);
    setToken(null);
    navigate("/login");
  };

  const value = {
    user,
    token,
    login,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
