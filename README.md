## Progress Tracker
### Day 1 (Milestone 1 – <PERSON><PERSON> & Auth)
- [x] #1 Scaffold backend + connect to MongoDB
- [x] #2 Create User model + Auth endpoints
- [x] #3 Build AuthContext + Login/Register pages

### Day 2 (Milestone 2 – Inventory & Item)
- [x] #4 Define Inventory schema + CRUD controllers/routes
- [x] #5 Define Item schema + CRUD controllers/routes
- [x] #6 Build InventoryList/InventoryForm components (UI + API calls)
- [ ] #7 Build ItemList/ItemForm components (UI + API calls)

### Day 3 (Milestone 3 – Print & Deploy)
- [ ] #8 Implement PrintableTable + print CSS
- [ ] #9 Responsive/Tailwind polish + loading/error states
- [ ] #10 Deploy backend & frontend on Render
- [ ] #11 Write README, API docs, and code comments
